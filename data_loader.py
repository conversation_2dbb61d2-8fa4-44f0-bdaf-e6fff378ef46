import sqlite3
import pandas as pd
import os

# --- 配置区域 ---
# 获取当前文件所在的绝对路径，并构建数据库文件的绝对路径
_current_dir = os.path.dirname(os.path.abspath(__file__))
DB_PATH = os.path.join(_current_dir, 'data', 'etf_quant.db')

def load_all_etf_data() -> pd.DataFrame:
    """
    从数据库加载所有ETF的日线行情数据。

    Returns:
        pd.DataFrame: 包含所有ETF日线数据的DataFrame，
                      索引为 'trade_date' (交易日期) 和 'etf_code' (ETF代码)，
                      数据按日期和代码排序。
    """
    try:
        with sqlite3.connect(DB_PATH) as conn:
            query = "SELECT * FROM etf_daily"
            df = pd.read_sql(query, conn, parse_dates=['trade_date'])
            
            # 设置多重索引，方便按日期和ETF代码进行查询
            df.set_index(['trade_date', 'etf_code'], inplace=True)
            df.sort_index(inplace=True)
            return df
    except Exception as e:
        print(f"Error loading ETF data: {e}")
        return pd.DataFrame()

def load_benchmark_data() -> pd.DataFrame:
    """
    从数据库加载沪深300指数的日线行情数据。

    Returns:
        pd.DataFrame: 包含沪深300日线数据的DataFrame，
                      索引为 'trade_date' (交易日期)，
                      数据按日期排序。
    """
    try:
        with sqlite3.connect(DB_PATH) as conn:
            query = "SELECT * FROM benchmark_daily"
            df = pd.read_sql(query, conn, parse_dates=['trade_date'])
            
            df.set_index('trade_date', inplace=True)
            df.sort_index(inplace=True)
            return df
    except Exception as e:
        print(f"Error loading benchmark data: {e}")
        return pd.DataFrame()

def load_etf_maps() -> (dict, dict):
    """
    从CSV文件加载ETF代码到名称和主题的映射。

    Returns:
        tuple: 包含两个字典 (code_to_name, code_to_theme)
    """
    map_file_path = os.path.join(os.path.dirname(_current_dir), '量化\常见主题ETF对照表.csv')
    try:
        df = pd.read_csv(map_file_path)
        # 确保代码列是字符串，并与yfinance格式对齐
        df['代码'] = df['代码'].astype(str).str.zfill(6) + '.' + df['上市地点']
        
        code_to_name = df.set_index('代码')['名称'].to_dict()
        code_to_theme = df.set_index('代码')['主题'].to_dict()
        
        return code_to_name, code_to_theme
    except FileNotFoundError:
        print(f"Error: Mapping file not found at {map_file_path}")
        return {}, {}
    except Exception as e:
        print(f"Error loading ETF maps: {e}")
        return {}, {}


if __name__ == '__main__':
    # --- 用于测试模块功能的代码 ---
    print("--- Testing New Data Loader ---")
    
    # 1. 测试加载所有ETF数据
    print("\nLoading all ETF data...")
    etf_data = load_all_etf_data()
    if not etf_data.empty:
        print(f"Successfully loaded {etf_data.index.get_level_values('etf_code').nunique()} ETFs data.")
        print("Total records:", len(etf_data))
        print("Data columns:", etf_data.columns.tolist())
        print("\nLast 5 records:")
        print(etf_data.tail(5))
    else:
        print("Failed to load ETF data.")

    # 2. 测试加载大盘基准数据
    print("\nLoading benchmark data (CSI 300)...")
    benchmark_data = load_benchmark_data()
    if not benchmark_data.empty:
        print("Successfully loaded benchmark data.")
        print("Total records:", len(benchmark_data))
        print("Data columns:", benchmark_data.columns.tolist())
        print("\nLast 5 records:")
        print(benchmark_data.tail(5))
    else:
        print("Failed to load benchmark data.")
