import pandas as pd
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import os

# 假设我们有一个 results_handler 模块来加载 .btr 文件
from results_handler import load_results
from data_loader import load_etf_maps, load_benchmark_data

def create_interactive_report(btr_data: dict, initial_capital: float = 30000.0):
    """
    根据回测结果数据，创建一个交互式的可视化报告。
    """
    # 1. 数据准备 和 名称映射
    # =================================
    results = btr_data['results']
    label = btr_data.get('label', 'Strategy')
    equity_curve = results['cumulative_returns']
    daily_returns = results['daily_returns']
    holdings_df = results.get('holdings')
    all_daily_returns = results.get('all_daily_returns') # 安全地获取数据

    code_to_name, code_to_theme = load_etf_maps()

    equity_curve_amount = equity_curve * initial_capital

    # 加载大盘基准数据
    benchmark_data = load_benchmark_data()
    if not benchmark_data.empty:
        # 计算基准的累计收益率，与策略时间段对齐
        benchmark_start_date = equity_curve.index[0]
        benchmark_end_date = equity_curve.index[-1]

        # 筛选基准数据到策略时间段
        benchmark_period = benchmark_data.loc[benchmark_start_date:benchmark_end_date]

        if not benchmark_period.empty:
            # 计算基准的累计收益率
            benchmark_returns = benchmark_period['close'].pct_change().fillna(0)
            benchmark_cumulative = (1 + benchmark_returns).cumprod()

            # 标准化到初始资金
            benchmark_cumulative = benchmark_cumulative / benchmark_cumulative.iloc[0]
            benchmark_amount = benchmark_cumulative * initial_capital

            # 重新索引以匹配策略数据
            benchmark_amount = benchmark_amount.reindex(equity_curve.index, method='ffill')
        else:
            benchmark_amount = None
    else:
        benchmark_amount = None
    
    def format_holdings(row, next_date=None):
        active_holdings = row[row > 0]
        if active_holdings.empty:
            return "CASH"

        lines = []
        for code, weight in active_holdings.items():
            name = code_to_name.get(code, code)
            # 【修复】使用下一个交易日的收益率，与组合收益率计算逻辑保持一致
            if (all_daily_returns is not None and next_date is not None and
                next_date in all_daily_returns.index and code in all_daily_returns.columns):
                daily_return = all_daily_returns.loc[next_date, code]
                lines.append(f"{name}: {weight:.0%} ({daily_return:+.2%})")
            else:
                lines.append(f"{name}: {weight:.0%}") # 降级处理
        return '<br>'.join(lines)

    # 【修复】为每一天的持仓计算对应的下一个交易日收益率
    hover_holdings_lines = []
    for i in range(len(holdings_df)):
        current_date = holdings_df.index[i]
        # 找到下一个交易日
        future_dates = holdings_df.index[holdings_df.index > current_date]
        next_date = future_dates[0] if len(future_dates) > 0 else None

        hover_text = format_holdings(holdings_df.iloc[i], next_date)
        hover_holdings_lines.append(hover_text)

    hover_holdings = pd.Series(hover_holdings_lines, index=holdings_df.index).shift(1).fillna("CASH")

    rebalance_dates = holdings_df.diff().abs().sum(axis=1).replace(0, float('nan')).dropna().index
    
    # 2. 创建图形和子图
    # =================================
    fig = make_subplots(
        rows=4, cols=1, shared_xaxes=True, vertical_spacing=0.03,
        row_heights=[0.55, 0.15, 0.15, 0.15]
    )

    # 3. 主图区：资金曲线
    # =================================
    fig.add_trace(go.Scattergl(
        x=equity_curve_amount.index, y=equity_curve_amount, mode='lines', name='策略收益',
        line=dict(color='dodgerblue', width=2),
        customdata=pd.DataFrame({'daily_return': daily_returns, 'holdings': hover_holdings}),
        hovertemplate='<b>%{x|%Y-%m-%d}</b><br>策略资金: %{y:,.2f}<br>日收益: %{customdata[0]:.2%}<br>--- 持仓 ---<br>%{customdata[1]}<extra></extra>'
    ), row=1, col=1)

    # 添加大盘基准线
    if benchmark_amount is not None:
        fig.add_trace(go.Scattergl(
            x=benchmark_amount.index, y=benchmark_amount, mode='lines', name='沪深300基准',
            line=dict(color='orange', width=2, dash='dash'),
            hovertemplate='<b>%{x|%Y-%m-%d}</b><br>基准资金: %{y:,.2f}<extra></extra>'
        ), row=1, col=1)

    fig.add_trace(go.Scatter(
        x=rebalance_dates, y=equity_curve_amount.loc[rebalance_dates], mode='markers', name='Rebalance Day',
        marker=dict(color='rgba(231, 76, 60, 0.7)', symbol='triangle-down', size=9, line=dict(width=1, color='black')),
        hoverinfo='skip'
    ), row=1, col=1)

    # 4. 副图一：按主题上色的持仓结构图
    # =================================
    shifted_holdings = holdings_df.shift(1).fillna(0)
    
    # 按主题分组
    holdings_by_theme = {}
    for code, series in shifted_holdings.items():
        theme = code_to_theme.get(code, 'Other')
        if theme not in holdings_by_theme:
            holdings_by_theme[theme] = series.copy()
        else:
            holdings_by_theme[theme] += series
    
    holdings_by_theme_df = pd.DataFrame(holdings_by_theme)
    holdings_by_theme_df['CASH'] = 1 - holdings_by_theme_df.sum(axis=1)
    
    import plotly.express as px
    color_map = px.colors.qualitative.Vivid
    
    for i, theme in enumerate(holdings_by_theme_df.columns):
        fig.add_trace(go.Scatter(
            x=holdings_by_theme_df.index, y=holdings_by_theme_df[theme],
            mode='lines', line=dict(width=0), stackgroup='themes', name=theme,
            fillcolor=color_map[i % len(color_map)],
            hovertemplate='%{y:.0%}<extra></extra>'
        ), row=2, col=1)

    # 5. 副图二 & 三：每日收益 和 回撤
    # =================================
    colors = ['#27AE60' if v >= 0 else '#E74C3C' for v in daily_returns]
    fig.add_trace(go.Bar(x=daily_returns.index, y=daily_returns, name='Daily Return', marker_color=colors, hovertemplate='%{y:.2%}<extra></extra>'), row=3, col=1)
    
    high_water_mark = equity_curve.cummax()
    drawdown = (equity_curve - high_water_mark) / high_water_mark
    fig.add_trace(go.Scatter(x=drawdown.index, y=drawdown, name='Drawdown', fill='tozeroy', line=dict(color='rgba(231, 76, 60, 0.5)'), hovertemplate='%{y:.2%}<extra></extra>'), row=4, col=1)

    # 6. 更新整体布局
    # =================================
    fig.update_layout(
        title=f'Interactive Backtest Report: <b>{label}</b>', height=900, template='plotly_white',
        legend=dict(orientation="h", yanchor="bottom", y=1.02, xanchor="right", x=1),
        xaxis_rangeslider_visible=False, hovermode='x unified'
    )
    fig.update_yaxes(title_text='Equity', row=1, col=1)
    fig.update_yaxes(title_text='Holdings by Theme', tickformat='.0%', row=2, col=1)
    fig.update_yaxes(title_text='Daily Return', tickformat='.2%', row=3, col=1)
    fig.update_yaxes(title_text='Drawdown', tickformat='.2%', row=4, col=1)
    
    for i in range(1, 5):
        fig.update_xaxes(title_text=None, row=i, col=1)

    return fig

if __name__ == '__main__':
    # --- 示例用法 ---
    # 1. 找到一个最新的 .btr 文件进行测试
    reports_dir = 'reports'
    btr_files = [f for f in os.listdir(reports_dir) if f.endswith('.btr')]
    if not btr_files:
        print("No .btr files found in 'reports' directory. Please run analysis scripts first.")
    else:
        # 选择最新的文件
        latest_file = max(btr_files, key=lambda f: os.path.getmtime(os.path.join(reports_dir, f)))
        print(f"Loading latest backtest result: {latest_file}")
        
        # 2. 加载数据
        btr_data = load_results(os.path.join(reports_dir, latest_file))
        
        if btr_data:
            # 3. 创建交互式报告
            fig = create_interactive_report(btr_data)
            
            # 4. 显示图表
            # fig.show() 会在浏览器中打开一个可交互的图表
            # 我们也可以保存为html文件
            report_path = os.path.join(reports_dir, f'interactive_report_{btr_data["label"]}.html')
            fig.write_html(report_path)
            print(f"Interactive report saved to: {report_path}")
            
            # 为了在IDE中直接看到效果，取消下面的注释
            # fig.show()
