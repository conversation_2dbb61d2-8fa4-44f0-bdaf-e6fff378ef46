import pandas as pd
import numpy as np
import data_loader

class BacktestEngine:
    """
    向量化量化策略回测引擎。
    
    核心思想是利用pandas的向量化操作，一次性计算所有信号和收益，
    避免按天循环，从而极大提升回测速度。
    """
    def __init__(self, params):
        """
        初始化回测引擎。
        
        Args:
            params (dict): 回测所需的核心参数，包括：
                - momentum_window (int): 动量计算周期（天）
                - ma_window (int): 大盘择时均线周期（天）
                - top_n (int): 持仓ETF的数量
                - start_date (str): 回测开始日期, 格式 'YYYY-MM-DD'
        """
        self.params = params
        self.etf_data = None
        self.benchmark_data = None
        self.results = {}

    def _prepare_data(self):
        """加载并预处理数据，计算因子和信号"""
        # 1. 加载【全部】数据，为指标计算提供充足的历史数据
        etf_data_full = data_loader.load_all_etf_data()
        benchmark_data_full = data_loader.load_benchmark_data()

        # 2. 数据清洗和准备
        close_prices = etf_data_full['close'].unstack(level='etf_code')
        daily_returns = close_prices.pct_change().fillna(0)

        # 3. 计算动量因子 (在全部数据上计算)
        momentum = close_prices.pct_change(self.params['momentum_window']).fillna(0)

        # 4. 计算大盘择时信号 (在全部数据上计算)
        benchmark_ma = benchmark_data_full['close'].rolling(window=self.params['ma_window']).mean()
        market_signal = (benchmark_data_full['close'] > benchmark_ma).astype(int)

        # 5. 【重要】根据回测周期，截断所有数据
        start_date = self.params.get('start_date', '2015-01-01')
        end_date = self.params.get('end_date', pd.to_datetime('today').strftime('%Y-%m-%d'))
        
        self.etf_data = close_prices.loc[start_date:end_date]
        self.daily_returns = daily_returns.loc[start_date:end_date]
        self.momentum = momentum.loc[start_date:end_date]
        self.market_signal = market_signal.loc[start_date:end_date]
        
        print(f"Data prepared and filtered from {start_date} to {end_date}.")

    def _run_backtest_vectorized(self):
        """执行向量化回测"""
        # 1. 确定调仓日 (Rebalancing Dates)
        rebalance_rule = self.params.get('rebalance_rule', 'last') # 默认为 last
        monthly_groups = self.momentum.index.to_period('M')
        
        if rebalance_rule == 'first':
            rebalance_dates = self.momentum.groupby(monthly_groups).apply(lambda x: x.index.min())
        elif rebalance_rule == 'middle':
            rebalance_dates = self.momentum.groupby(monthly_groups).apply(lambda x: x.index[len(x) // 2])
        else: # last
            rebalance_dates = self.momentum.groupby(monthly_groups).apply(lambda x: x.index.max())

        # 2. 生成持仓权重
        # 创建一个与动量数据同样大小，但初始值全为0的DataFrame，用于存储每日持仓
        holdings = pd.DataFrame(0.0, index=self.momentum.index, columns=self.momentum.columns)

        for i in range(len(rebalance_dates) - 1):
            start_date = rebalance_dates.iloc[i]
            end_date = rebalance_dates.iloc[i+1]

            # 获取调仓日的动量排名
            momentum_on_rebalance_day = self.momentum.loc[start_date]
            
            # 过滤掉没有数据的ETF (动量为0或NaN)
            valid_momentum = momentum_on_rebalance_day[momentum_on_rebalance_day > -1] # pct_change может быть -1
            
            # 【重要】根据牛熊市信号，动态决定持仓数量和总资金分配
            is_bull_market = self.market_signal.loc[start_date] == 1
            
            # 牛市满仓(1.0)，熊市则使用预设的仓位比例，默认为0
            allocation = 1.0 if is_bull_market else self.params.get('bear_market_allocation', 0.0)
            n_to_hold = self.params['top_n'] if is_bull_market else self.params.get('bear_market_hold_n', 0)

            # 确定当天实际可投资的ETF数量
            num_valid_etfs = len(valid_momentum)
            # 实际持仓数 = min(规则持仓数, 当天有效ETF数)
            n_to_hold = min(n_to_hold, num_valid_etfs)

            if n_to_hold > 0:
                top_performers = valid_momentum.nlargest(n_to_hold).index
                # 【核心修正】权重 = 总资金分配 / 持仓数量
                weight = allocation / n_to_hold

                # 【重要修复】先清零该时间段的所有持仓，再设置新的持仓权重
                holdings.loc[start_date:end_date, :] = 0.0
                holdings.loc[start_date:end_date, top_performers] = weight
            else:
                # 如果不持仓，确保该时间段所有权重为0
                holdings.loc[start_date:end_date, :] = 0.0

        # 3. 【逻辑修正】应用大盘择时信号的逻辑已经包含在上面`n_to_hold`的计算中，
        #    此处不再需要重复应用 market_signal，否则会导致熊市持仓被错误清零。
        # timing_enabled = self.params.get('timing_enabled', True)
        # if timing_enabled:
        #     final_holdings = holdings.multiply(self.market_signal, axis='index')
        # else:
        #     final_holdings = holdings
        final_holdings = holdings

        # 4. 计算投资组合每日收益
        # 组合收益 = 持仓权重 * 第二天的日收益率 (因为今天的决策决定了明天的收益)
        # 我们将权重向下移动一天 (shift(1)) 来与第二天的收益对齐
        portfolio_returns = (final_holdings.shift(1) * self.daily_returns).sum(axis=1)

        # 5. 计算并存储最终结果
        self.results['daily_returns'] = portfolio_returns
        self.results['cumulative_returns'] = (1 + portfolio_returns).cumprod()
        self.results['market_signal'] = self.market_signal
        self.results['holdings'] = final_holdings
        self.results['all_daily_returns'] = self.daily_returns # <-- 新增：返回所有ETF的日收益数据
        print("Vectorized backtest complete.")

    def run(self):
        """完整执行回测流程"""
        self._prepare_data()
        self._run_backtest_vectorized()
        return self.results

if __name__ == '__main__':
    # --- 复现V2策略 (2020年之后版本) ---
    # 动量周期=20天，持有行业数=3个，择时均线=200天
    v2_params = {
        'momentum_window': 20,
        'ma_window': 200,
        'top_n': 3,
        'start_date': '2020-01-01' # 新增参数
    }

    print("--- Running Backtest for Strategy (Post-2020) ---")
    engine = BacktestEngine(params=v2_params)
    results = engine.run()

    print("\n--- Backtest Performance ---")
    for metric, value in results.get('performance', {}).items():
        print(f"{metric:<20}: {value}")

    # 可以在这里选择性地画图
    # results['cumulative_returns'].plot(title='V2 Strategy Cumulative Returns')
    # import matplotlib.pyplot as plt
    # plt.show()