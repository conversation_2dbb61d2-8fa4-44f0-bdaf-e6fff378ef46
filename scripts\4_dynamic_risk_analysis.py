import pandas as pd
import matplotlib.pyplot as plt
import os
import warnings
from datetime import datetime

# 动态地将上级目录添加到sys.path，以便能找到项目根目录下的模块
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from backtest_engine import BacktestEngine
from evaluation import StrategyEvaluator, plot_scorecard_radar
from results_handler import save_results
import data_loader

# ==============================================================================
# --- 核心参数配置区 ---
# ==============================================================================

# 1. 回测周期
START_DATE = '2018-01-15'
END_DATE = pd.to_datetime('today').strftime('%Y-%m-%d')

# 2. 策略核心参数
BASE_PARAMS = {
    'momentum_window': 30,
    'ma_window': 200,
    'top_n': 3, # 牛市持仓数
    'start_date': START_DATE
}

# 3. 测试变量：熊市总仓位配置
BEAR_ALLOCATION_LIST = [1.0, 0.5, 0.33, 0.0]

# 4. 文件保存设置
REPORTS_DIR = 'reports'
# 使用时间戳确保文件名唯一
TIMESTAMP = datetime.now().strftime('%Y%m%d_%H%M%S')
CURVE_CHART_PATH = os.path.join(REPORTS_DIR, f'dynamic_risk_curves_{TIMESTAMP}.png')
RADAR_CHART_PATH = os.path.join(REPORTS_DIR, f'dynamic_risk_radar_{TIMESTAMP}.png')

# ==============================================================================

def run_dynamic_risk_analysis():
    """执行动态风险（软择时）策略的对比分析"""
    
    project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    reports_path = os.path.join(project_root, REPORTS_DIR)
    os.makedirs(reports_path, exist_ok=True)
    
    all_results_for_eval = {}

    print(f"开始进行动态风险策略分析，共 {len(BEAR_ALLOCATION_LIST)} 组测试...")

    with warnings.catch_warnings():
        warnings.simplefilter("ignore", FutureWarning)
        
        for i, allocation in enumerate(BEAR_ALLOCATION_LIST):
            params = BASE_PARAMS.copy()
            params['bear_market_allocation'] = allocation
            params['bear_market_hold_n'] = 1 if allocation > 0 else 0

            label = f"BearAlloc={allocation*100:.0f}%"
            if allocation == 1.0:
                label = "Pure Momentum"
            elif allocation == 0.0:
                label = "Hard-Timing"

            print(f"  Running Test {i+1}/{len(BEAR_ALLOCATION_LIST)}: {label}...")

            engine = BacktestEngine(params=params)
            results = engine.run()
            
            # --- 核心改动：保存详细的回测结果到 .btr 文件 ---
            btr_data = {'params': params, 'results': results, 'label': label}
            btr_filename = f"dynamic_risk_{label}_{TIMESTAMP}.btr"
            btr_filepath = os.path.join(reports_path, btr_filename)
            save_results(btr_data, btr_filepath)
            
            all_results_for_eval[label] = results

    # --- 2. 生成并打印最终的评分卡 ---
    scorecard = StrategyEvaluator.generate_scorecard(all_results_for_eval)
    print("\n\n--- 动态风险策略评分卡 (绝对评分) ---")
    pd.set_option('display.width', 200)
    display_cols = ['total_score', 'profitability_score', 'risk_score', 'risk_adjusted_return_score', 'consistency_score']
    print(scorecard[display_cols])

    # --- 3. 绘制并保存收益曲线对比图 (面向对象模式) ---
    all_curves = {label: res['cumulative_returns'] for label, res in all_results_for_eval.items()}
    benchmark_data = data_loader.load_benchmark_data()
    benchmark_curve = benchmark_data.loc[START_DATE:END_DATE]['close']
    all_curves['CSI 300 Benchmark'] = (1 + benchmark_curve.pct_change().fillna(0)).cumprod()

    plt.close('all')
    fig, ax = plt.subplots(figsize=(14, 8))
    pd.DataFrame(all_curves).plot(ax=ax, grid=True)
    
    ax.set_title(f'Dynamic Risk Strategy Comparison ({START_DATE} to {END_DATE})', fontsize=16)
    ax.set_ylabel('Cumulative Return')
    ax.set_xlabel('Date')
    ax.legend()
    
    curve_save_path = os.path.join(reports_path, os.path.basename(CURVE_CHART_PATH))
    fig.savefig(curve_save_path)
    print(f"\n收益曲线图已保存至: {curve_save_path}")
    plt.close(fig)

    # --- 4. 绘制并保存雷达图 ---
    radar_save_path = os.path.join(reports_path, os.path.basename(RADAR_CHART_PATH))
    plot_scorecard_radar(scorecard, top_n=len(BEAR_ALLOCATION_LIST), save_path=radar_save_path)
    
    print("\n分析完成！")

if __name__ == '__main__':
    run_dynamic_risk_analysis()
